/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "query AreasOfNeed {\n  allAreaOfNeeds(first: \"500\") {\n    id\n    title\n    maximumScore\n    minimumScore\n    testArea\n    linkUrl\n    linkTitle\n    flagColour\n    descriptionTeacher(markdown: true)\n    descriptionParent(markdown: true)\n    description(markdown: true)\n    icon {\n      url\n    }\n  }\n}\n\nquery AreasOfNeedFallback {\n  general {\n    aonFallbackTitle\n    aonFallbackDescription(markdown: true)\n    aonFallbackIcon {\n      url\n    }\n  }\n}": typeof types.AreasOfNeedDocument,
    "query GetNovaAssessment($id: ItemId!) {\n  novaAssessment(filter: {id: {eq: $id}}) {\n    __typename\n    id\n    displayName\n    assessmentType\n    audience\n    shuffle\n    nudge\n    nudgeTimerDurationS\n    tutorialVideo {\n      url\n    }\n    assessmentContent: content {\n      __typename\n      ... on NovaScaleRecord {\n        id\n        internalId\n        displayName\n        icon {\n          url\n          responsiveImage {\n            ...ResponsiveImageFields\n          }\n        }\n        disabledIcon {\n          url\n          responsiveImage {\n            ...ResponsiveImageFields\n          }\n        }\n        scaleTimer\n        scaleTimerDuration\n        nudge\n        nudgeTimerDuration\n        kickOut\n        kickOutThreshold\n        countdownAnimation {\n          url\n        }\n        completeAnimation {\n          url\n        }\n        scaleContent: content {\n          __typename\n          ... on NovaScaleItemRecord {\n            id\n            internalId\n            relevantAges\n            practiceItem\n            feedback\n            skippable\n            disabledWhileAutoplay\n            tutorial\n            stimulusType\n            stimulusText\n            stimulusMedia {\n              url\n            }\n            audioPlays\n            responseType\n            responseMedia {\n              id\n              url\n              title\n              filename\n              smallImage: responsiveImage(imgixParams: {w: \"136\", h: \"136\", q: 60}) {\n                ...ResponsiveImageFields\n              }\n              mediumImage: responsiveImage(imgixParams: {w: \"176\", h: \"176\", q: 60}) {\n                ...ResponsiveImageFields\n              }\n            }\n            responseText\n            gridColumns\n            minimumResponsesRequired\n            maximumResponsesAllowed\n            correctAnswer\n            ifCorrectText\n            ifCorrectAudio {\n              url\n            }\n            ifCorrectImage {\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n            ifIncorrectAction\n            ifIncorrectText\n            ifIncorrectAudio {\n              url\n            }\n            ifIncorrectImage {\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n            tutorialText\n            tutorialAudio {\n              url\n            }\n            tutorialImage {\n              url\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n          }\n        }\n      }\n    }\n    icon {\n      url\n    }\n  }\n}": typeof types.GetNovaAssessmentDocument,
    "fragment ResponsiveImageFields on ResponsiveImage {\n  alt\n  aspectRatio\n  base64\n  bgColor\n  height\n  sizes\n  src\n  srcSet\n  title\n  webpSrcSet\n  width\n}": typeof types.ResponsiveImageFieldsFragmentDoc,
    "fragment VideoFields on UploadVideoField {\n  width\n  title\n  thumbnailUrl\n  thumbhash\n  streamingUrl\n  muxPlaybackId\n  muxAssetId\n  mp4Url\n  height\n  framerate\n  duration\n  blurUpThumb\n  alt\n  blurhash\n}": typeof types.VideoFieldsFragmentDoc,
    "query CoverPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"cover\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery SupportPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"support\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery ResultsPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"results\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery AppendixPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"appendix\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery ContentsPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"contents\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}": typeof types.CoverPdfResourcesDocument,
    "query ReportRecommendationsCount {\n  _allRecommendationsMeta {\n    count\n  }\n}\n\nquery ReportRecommendations($first: IntType!, $skip: IntType!) {\n  allRecommendations(first: $first, skip: $skip) {\n    id\n    title\n    description(markdown: true)\n    category\n    recommendationType\n    testArea\n    scoringType\n    relevantAges\n    minimumScore\n    maximumScore\n    primaryLink {\n      title\n      url\n      description(markdown: true)\n    }\n    secondaryContent {\n      id\n      title\n      url\n      description(markdown: true)\n    }\n    recommendationType\n    duration\n  }\n}\n\nquery RecommendationTypeIcons {\n  general {\n    recommendationTypeIcon {\n      title\n      url\n    }\n  }\n}\n\nquery RecommendationCopy {\n  general {\n    highPriorityCopy(markdown: true)\n    highPriorityFallbackCopy(markdown: true)\n    extraSupportCopy(markdown: true)\n    classroomCopy(markdown: true)\n    atHomeCopy(markdown: true)\n    recommendationsIntroductionCopy(markdown: true)\n  }\n}": typeof types.ReportRecommendationsCountDocument,
    "query RiskLevels {\n  allRiskLevels(orderBy: position_ASC) {\n    id\n    title\n    description\n    riskType\n    requiredRiskLevel\n    descriptionTeacher\n    descriptionParent\n  }\n}\n\nquery GeneralResults {\n  general {\n    risksLinkTitle\n    risksLinkUrl\n  }\n}": typeof types.RiskLevelsDocument,
};
const documents: Documents = {
    "query AreasOfNeed {\n  allAreaOfNeeds(first: \"500\") {\n    id\n    title\n    maximumScore\n    minimumScore\n    testArea\n    linkUrl\n    linkTitle\n    flagColour\n    descriptionTeacher(markdown: true)\n    descriptionParent(markdown: true)\n    description(markdown: true)\n    icon {\n      url\n    }\n  }\n}\n\nquery AreasOfNeedFallback {\n  general {\n    aonFallbackTitle\n    aonFallbackDescription(markdown: true)\n    aonFallbackIcon {\n      url\n    }\n  }\n}": types.AreasOfNeedDocument,
    "query GetNovaAssessment($id: ItemId!) {\n  novaAssessment(filter: {id: {eq: $id}}) {\n    __typename\n    id\n    displayName\n    assessmentType\n    audience\n    shuffle\n    nudge\n    nudgeTimerDurationS\n    tutorialVideo {\n      url\n    }\n    assessmentContent: content {\n      __typename\n      ... on NovaScaleRecord {\n        id\n        internalId\n        displayName\n        icon {\n          url\n          responsiveImage {\n            ...ResponsiveImageFields\n          }\n        }\n        disabledIcon {\n          url\n          responsiveImage {\n            ...ResponsiveImageFields\n          }\n        }\n        scaleTimer\n        scaleTimerDuration\n        nudge\n        nudgeTimerDuration\n        kickOut\n        kickOutThreshold\n        countdownAnimation {\n          url\n        }\n        completeAnimation {\n          url\n        }\n        scaleContent: content {\n          __typename\n          ... on NovaScaleItemRecord {\n            id\n            internalId\n            relevantAges\n            practiceItem\n            feedback\n            skippable\n            disabledWhileAutoplay\n            tutorial\n            stimulusType\n            stimulusText\n            stimulusMedia {\n              url\n            }\n            audioPlays\n            responseType\n            responseMedia {\n              id\n              url\n              title\n              filename\n              smallImage: responsiveImage(imgixParams: {w: \"136\", h: \"136\", q: 60}) {\n                ...ResponsiveImageFields\n              }\n              mediumImage: responsiveImage(imgixParams: {w: \"176\", h: \"176\", q: 60}) {\n                ...ResponsiveImageFields\n              }\n            }\n            responseText\n            gridColumns\n            minimumResponsesRequired\n            maximumResponsesAllowed\n            correctAnswer\n            ifCorrectText\n            ifCorrectAudio {\n              url\n            }\n            ifCorrectImage {\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n            ifIncorrectAction\n            ifIncorrectText\n            ifIncorrectAudio {\n              url\n            }\n            ifIncorrectImage {\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n            tutorialText\n            tutorialAudio {\n              url\n            }\n            tutorialImage {\n              url\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n          }\n        }\n      }\n    }\n    icon {\n      url\n    }\n  }\n}": types.GetNovaAssessmentDocument,
    "fragment ResponsiveImageFields on ResponsiveImage {\n  alt\n  aspectRatio\n  base64\n  bgColor\n  height\n  sizes\n  src\n  srcSet\n  title\n  webpSrcSet\n  width\n}": types.ResponsiveImageFieldsFragmentDoc,
    "fragment VideoFields on UploadVideoField {\n  width\n  title\n  thumbnailUrl\n  thumbhash\n  streamingUrl\n  muxPlaybackId\n  muxAssetId\n  mp4Url\n  height\n  framerate\n  duration\n  blurUpThumb\n  alt\n  blurhash\n}": types.VideoFieldsFragmentDoc,
    "query CoverPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"cover\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery SupportPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"support\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery ResultsPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"results\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery AppendixPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"appendix\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery ContentsPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"contents\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}": types.CoverPdfResourcesDocument,
    "query ReportRecommendationsCount {\n  _allRecommendationsMeta {\n    count\n  }\n}\n\nquery ReportRecommendations($first: IntType!, $skip: IntType!) {\n  allRecommendations(first: $first, skip: $skip) {\n    id\n    title\n    description(markdown: true)\n    category\n    recommendationType\n    testArea\n    scoringType\n    relevantAges\n    minimumScore\n    maximumScore\n    primaryLink {\n      title\n      url\n      description(markdown: true)\n    }\n    secondaryContent {\n      id\n      title\n      url\n      description(markdown: true)\n    }\n    recommendationType\n    duration\n  }\n}\n\nquery RecommendationTypeIcons {\n  general {\n    recommendationTypeIcon {\n      title\n      url\n    }\n  }\n}\n\nquery RecommendationCopy {\n  general {\n    highPriorityCopy(markdown: true)\n    highPriorityFallbackCopy(markdown: true)\n    extraSupportCopy(markdown: true)\n    classroomCopy(markdown: true)\n    atHomeCopy(markdown: true)\n    recommendationsIntroductionCopy(markdown: true)\n  }\n}": types.ReportRecommendationsCountDocument,
    "query RiskLevels {\n  allRiskLevels(orderBy: position_ASC) {\n    id\n    title\n    description\n    riskType\n    requiredRiskLevel\n    descriptionTeacher\n    descriptionParent\n  }\n}\n\nquery GeneralResults {\n  general {\n    risksLinkTitle\n    risksLinkUrl\n  }\n}": types.RiskLevelsDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query AreasOfNeed {\n  allAreaOfNeeds(first: \"500\") {\n    id\n    title\n    maximumScore\n    minimumScore\n    testArea\n    linkUrl\n    linkTitle\n    flagColour\n    descriptionTeacher(markdown: true)\n    descriptionParent(markdown: true)\n    description(markdown: true)\n    icon {\n      url\n    }\n  }\n}\n\nquery AreasOfNeedFallback {\n  general {\n    aonFallbackTitle\n    aonFallbackDescription(markdown: true)\n    aonFallbackIcon {\n      url\n    }\n  }\n}"): (typeof documents)["query AreasOfNeed {\n  allAreaOfNeeds(first: \"500\") {\n    id\n    title\n    maximumScore\n    minimumScore\n    testArea\n    linkUrl\n    linkTitle\n    flagColour\n    descriptionTeacher(markdown: true)\n    descriptionParent(markdown: true)\n    description(markdown: true)\n    icon {\n      url\n    }\n  }\n}\n\nquery AreasOfNeedFallback {\n  general {\n    aonFallbackTitle\n    aonFallbackDescription(markdown: true)\n    aonFallbackIcon {\n      url\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query GetNovaAssessment($id: ItemId!) {\n  novaAssessment(filter: {id: {eq: $id}}) {\n    __typename\n    id\n    displayName\n    assessmentType\n    audience\n    shuffle\n    nudge\n    nudgeTimerDurationS\n    tutorialVideo {\n      url\n    }\n    assessmentContent: content {\n      __typename\n      ... on NovaScaleRecord {\n        id\n        internalId\n        displayName\n        icon {\n          url\n          responsiveImage {\n            ...ResponsiveImageFields\n          }\n        }\n        disabledIcon {\n          url\n          responsiveImage {\n            ...ResponsiveImageFields\n          }\n        }\n        scaleTimer\n        scaleTimerDuration\n        nudge\n        nudgeTimerDuration\n        kickOut\n        kickOutThreshold\n        countdownAnimation {\n          url\n        }\n        completeAnimation {\n          url\n        }\n        scaleContent: content {\n          __typename\n          ... on NovaScaleItemRecord {\n            id\n            internalId\n            relevantAges\n            practiceItem\n            feedback\n            skippable\n            disabledWhileAutoplay\n            tutorial\n            stimulusType\n            stimulusText\n            stimulusMedia {\n              url\n            }\n            audioPlays\n            responseType\n            responseMedia {\n              id\n              url\n              title\n              filename\n              smallImage: responsiveImage(imgixParams: {w: \"136\", h: \"136\", q: 60}) {\n                ...ResponsiveImageFields\n              }\n              mediumImage: responsiveImage(imgixParams: {w: \"176\", h: \"176\", q: 60}) {\n                ...ResponsiveImageFields\n              }\n            }\n            responseText\n            gridColumns\n            minimumResponsesRequired\n            maximumResponsesAllowed\n            correctAnswer\n            ifCorrectText\n            ifCorrectAudio {\n              url\n            }\n            ifCorrectImage {\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n            ifIncorrectAction\n            ifIncorrectText\n            ifIncorrectAudio {\n              url\n            }\n            ifIncorrectImage {\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n            tutorialText\n            tutorialAudio {\n              url\n            }\n            tutorialImage {\n              url\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n          }\n        }\n      }\n    }\n    icon {\n      url\n    }\n  }\n}"): (typeof documents)["query GetNovaAssessment($id: ItemId!) {\n  novaAssessment(filter: {id: {eq: $id}}) {\n    __typename\n    id\n    displayName\n    assessmentType\n    audience\n    shuffle\n    nudge\n    nudgeTimerDurationS\n    tutorialVideo {\n      url\n    }\n    assessmentContent: content {\n      __typename\n      ... on NovaScaleRecord {\n        id\n        internalId\n        displayName\n        icon {\n          url\n          responsiveImage {\n            ...ResponsiveImageFields\n          }\n        }\n        disabledIcon {\n          url\n          responsiveImage {\n            ...ResponsiveImageFields\n          }\n        }\n        scaleTimer\n        scaleTimerDuration\n        nudge\n        nudgeTimerDuration\n        kickOut\n        kickOutThreshold\n        countdownAnimation {\n          url\n        }\n        completeAnimation {\n          url\n        }\n        scaleContent: content {\n          __typename\n          ... on NovaScaleItemRecord {\n            id\n            internalId\n            relevantAges\n            practiceItem\n            feedback\n            skippable\n            disabledWhileAutoplay\n            tutorial\n            stimulusType\n            stimulusText\n            stimulusMedia {\n              url\n            }\n            audioPlays\n            responseType\n            responseMedia {\n              id\n              url\n              title\n              filename\n              smallImage: responsiveImage(imgixParams: {w: \"136\", h: \"136\", q: 60}) {\n                ...ResponsiveImageFields\n              }\n              mediumImage: responsiveImage(imgixParams: {w: \"176\", h: \"176\", q: 60}) {\n                ...ResponsiveImageFields\n              }\n            }\n            responseText\n            gridColumns\n            minimumResponsesRequired\n            maximumResponsesAllowed\n            correctAnswer\n            ifCorrectText\n            ifCorrectAudio {\n              url\n            }\n            ifCorrectImage {\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n            ifIncorrectAction\n            ifIncorrectText\n            ifIncorrectAudio {\n              url\n            }\n            ifIncorrectImage {\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n            tutorialText\n            tutorialAudio {\n              url\n            }\n            tutorialImage {\n              url\n              responsiveImage {\n                ...ResponsiveImageFields\n              }\n            }\n          }\n        }\n      }\n    }\n    icon {\n      url\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment ResponsiveImageFields on ResponsiveImage {\n  alt\n  aspectRatio\n  base64\n  bgColor\n  height\n  sizes\n  src\n  srcSet\n  title\n  webpSrcSet\n  width\n}"): (typeof documents)["fragment ResponsiveImageFields on ResponsiveImage {\n  alt\n  aspectRatio\n  base64\n  bgColor\n  height\n  sizes\n  src\n  srcSet\n  title\n  webpSrcSet\n  width\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment VideoFields on UploadVideoField {\n  width\n  title\n  thumbnailUrl\n  thumbhash\n  streamingUrl\n  muxPlaybackId\n  muxAssetId\n  mp4Url\n  height\n  framerate\n  duration\n  blurUpThumb\n  alt\n  blurhash\n}"): (typeof documents)["fragment VideoFields on UploadVideoField {\n  width\n  title\n  thumbnailUrl\n  thumbhash\n  streamingUrl\n  muxPlaybackId\n  muxAssetId\n  mp4Url\n  height\n  framerate\n  duration\n  blurUpThumb\n  alt\n  blurhash\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query CoverPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"cover\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery SupportPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"support\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery ResultsPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"results\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery AppendixPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"appendix\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery ContentsPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"contents\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}"): (typeof documents)["query CoverPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"cover\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery SupportPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"support\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery ResultsPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"results\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery AppendixPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"appendix\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}\n\nquery ContentsPDFResources {\n  allPdfResourceSets(filter: {name: {eq: \"contents\"}}) {\n    resources {\n      id\n      key\n      teacherValue(markdown: true)\n      value(markdown: true)\n      parentValue(markdown: true)\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ReportRecommendationsCount {\n  _allRecommendationsMeta {\n    count\n  }\n}\n\nquery ReportRecommendations($first: IntType!, $skip: IntType!) {\n  allRecommendations(first: $first, skip: $skip) {\n    id\n    title\n    description(markdown: true)\n    category\n    recommendationType\n    testArea\n    scoringType\n    relevantAges\n    minimumScore\n    maximumScore\n    primaryLink {\n      title\n      url\n      description(markdown: true)\n    }\n    secondaryContent {\n      id\n      title\n      url\n      description(markdown: true)\n    }\n    recommendationType\n    duration\n  }\n}\n\nquery RecommendationTypeIcons {\n  general {\n    recommendationTypeIcon {\n      title\n      url\n    }\n  }\n}\n\nquery RecommendationCopy {\n  general {\n    highPriorityCopy(markdown: true)\n    highPriorityFallbackCopy(markdown: true)\n    extraSupportCopy(markdown: true)\n    classroomCopy(markdown: true)\n    atHomeCopy(markdown: true)\n    recommendationsIntroductionCopy(markdown: true)\n  }\n}"): (typeof documents)["query ReportRecommendationsCount {\n  _allRecommendationsMeta {\n    count\n  }\n}\n\nquery ReportRecommendations($first: IntType!, $skip: IntType!) {\n  allRecommendations(first: $first, skip: $skip) {\n    id\n    title\n    description(markdown: true)\n    category\n    recommendationType\n    testArea\n    scoringType\n    relevantAges\n    minimumScore\n    maximumScore\n    primaryLink {\n      title\n      url\n      description(markdown: true)\n    }\n    secondaryContent {\n      id\n      title\n      url\n      description(markdown: true)\n    }\n    recommendationType\n    duration\n  }\n}\n\nquery RecommendationTypeIcons {\n  general {\n    recommendationTypeIcon {\n      title\n      url\n    }\n  }\n}\n\nquery RecommendationCopy {\n  general {\n    highPriorityCopy(markdown: true)\n    highPriorityFallbackCopy(markdown: true)\n    extraSupportCopy(markdown: true)\n    classroomCopy(markdown: true)\n    atHomeCopy(markdown: true)\n    recommendationsIntroductionCopy(markdown: true)\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query RiskLevels {\n  allRiskLevels(orderBy: position_ASC) {\n    id\n    title\n    description\n    riskType\n    requiredRiskLevel\n    descriptionTeacher\n    descriptionParent\n  }\n}\n\nquery GeneralResults {\n  general {\n    risksLinkTitle\n    risksLinkUrl\n  }\n}"): (typeof documents)["query RiskLevels {\n  allRiskLevels(orderBy: position_ASC) {\n    id\n    title\n    description\n    riskType\n    requiredRiskLevel\n    descriptionTeacher\n    descriptionParent\n  }\n}\n\nquery GeneralResults {\n  general {\n    risksLinkTitle\n    risksLinkUrl\n  }\n}"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;