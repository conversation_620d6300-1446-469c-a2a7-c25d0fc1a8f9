import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { supabaseConfig } from "@/data/constants";
import { ReportVersion } from "@/types/reporting";

const ConfidenceIntervalSchema = z.object({
  lowerBound: z.number(),
  upperBound: z.number(),
});

const ScaleSchema = z.object({
  scaleId: z.string(),
  rawScore: z.number(),
  scaleName: z.string(),
  standardScore: z.number(),
  percentileRank: z.number(),
  scoreRank: z.string(),
  unusualResults: z.boolean(),
  confidenceInterval: ConfidenceIntervalSchema,
});

const CompositeSchema = z.object({
  standardScore: z.number(),
  percentileRank: z.number(),
  scoreRank: z.string(),
  unusualResults: z.boolean(),
  confidenceInterval: ConfidenceIntervalSchema,
});

const CognitiveProfileAreaSchema = z.object({
  scales: z.array(ScaleSchema),
  composite: CompositeSchema,
});

const CognitiveProfileSchema = z.object({
  spelling: CognitiveProfileAreaSchema,
  readingSpeed: CognitiveProfileAreaSchema,
  workingMemory: CognitiveProfileAreaSchema,
  processingSpeed: CognitiveProfileAreaSchema,
  verbalReasoning: CognitiveProfileAreaSchema,
  visualReasoning: CognitiveProfileAreaSchema,
  readingComprehension: CognitiveProfileAreaSchema,
  phonologicalAwareness: CognitiveProfileAreaSchema,
});

const AssessmentMetaSchema = z.object({
  assessmentId: z.string().min(1),
  assessmentName: z.string().min(1),
  dateTaken: z.string().datetime(),
  schoolId: z.number().positive(),
  userId: z.union([z.string().min(1), z.number().positive()]),
});

const AnalysisSchema = z.object({
  dyslexiaRisk: z.number().min(1).max(5),
  dyslexiaScore: z.number(),
  levelOfNeedRisk: z.number().min(1).max(5),
  levelOfNeedScore: z.number(),
});

const ResultsDataSchema = z.object({
  cognitiveProfile: CognitiveProfileSchema,
});

const SaveReportRequestSchema = z.object({
  userID: z.union([z.string(), z.number()]).optional(),
  studentAge: z.number().positive(),
  studentCode: z.string().min(1),
  assessmentMeta: AssessmentMetaSchema,
  resultsData: ResultsDataSchema,
  analysis: AnalysisSchema,
  assessmentDataId: z.string().min(1),
  version: z.nativeEnum(ReportVersion),
});

export async function POST(request: NextRequest) {
  try {
    const bearerToken = request.headers
      .get("authorization")
      ?.split("Bearer ")[1];

    if (!bearerToken || bearerToken !== process.env.API_TOKEN) {
      console.log("[save-report] Auth failed:", {
        hasToken: !!bearerToken,
        tokenMatch: bearerToken === process.env.API_TOKEN,
      });
      return NextResponse.json(
        { message: "Please provide a valid bearer token" },
        { status: 401 }
      );
    }

    const requestBody = await request.json();

    const validationResult = SaveReportRequestSchema.safeParse(requestBody);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          message: "Validation failed",
          errors: validationResult.error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    const supabase = createClient(
      supabaseConfig.supabaseUrl,
      supabaseConfig.supabaseKey
    );

    const {
      studentCode,
      assessmentMeta,
      resultsData,
      analysis,
      assessmentDataId,
      version,
    } = data;

    const { error: supabaseError } = await supabase
      .from("assessment_results")
      .upsert(
        {
          student_code: studentCode,
          assessment_id: assessmentMeta.assessmentId,
          assessment_data_id: assessmentDataId,
          assessment_meta: assessmentMeta,
          result_data: resultsData,
          analysis: analysis,
          school_id: assessmentMeta.schoolId,
          version: version,
        },
        {
          onConflict: "student_code,assessment_id",
          ignoreDuplicates: false,
        }
      );

    if (supabaseError) {
      return NextResponse.json(
        {
          message: "Failed to save report",
          error: supabaseError.message,
          code: supabaseError.code,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Successfully saved to reports",
      studentCode,
      assessmentId: assessmentMeta.assessmentId,
    });
  } catch (error) {
    console.error("Unexpected error in save-report:", {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    });
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
