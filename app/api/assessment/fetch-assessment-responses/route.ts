import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { supabaseConfig } from "@/data/constants";

export async function GET(request: NextRequest) {
  const supabase = createClient(
    supabaseConfig.supabaseUrl,
    supabaseConfig.supabaseKey
  );

  const { searchParams } = new URL(request.url);
  const student_code = searchParams.get("student_code");
  const assessment_id = searchParams.get("assessment_id");

  if (!student_code || !assessment_id) {
    return NextResponse.json(
      { message: "Missing student_code or assessment_id" },
      { status: 400 }
    );
  }

  try {
    const { data: assessment, error: assessmentError } = await supabase
      .from("assessments")
      .select(
        "id, assessment_name, assessment_type, assessment_audience, total_time_ms, completed_at, is_complete"
      )
      .eq("student_code", student_code)
      .eq("assessment_id", assessment_id)
      .maybeSingle();

    console.log(assessmentError);

    if (assessmentError || !assessment) {
      return NextResponse.json(
        { message: "Assessment not found" },
        { status: 404 }
      );
    }

    const assessment_result_id = assessment.id;

    const { data: scales, error: scalesError } = await supabase
      .from("assessment_scales")
      .select("id, scale_id, scale_name, total_time_ms, reason_for_exit")
      .eq("student_code", student_code)
      .eq("assessment_id", assessment_result_id);

    if (scalesError || !scales?.length) {
      return NextResponse.json(
        { message: "Scales not found" },
        { status: 404 }
      );
    }

    const scaleIds = scales.map((s) => s.id);
    const { data: items, error: itemsError } = await supabase
      .from("assessment_scale_items")
      .select(
        "scale_id, item_id, name, seen, score, answer, skipped, practice, time_taken_ms"
      )
      .eq("student_code", student_code)
      .in("scale_id", scaleIds);

    if (itemsError) {
      return NextResponse.json(
        { message: "Items not found", error: itemsError },
        { status: 404 }
      );
    }

    const reconstructed = {
      id: assessment_id,
      name: assessment.assessment_name,
      assessmentType: assessment.assessment_type,
      assessmentAudience: assessment.assessment_audience,
      totalTimeMs: assessment.total_time_ms,
      dateCompleted: assessment.completed_at
        ? new Date(assessment.completed_at).toISOString()
        : null,
      isComplete: assessment.is_complete,
      scales: scales.map((scale) => ({
        id: scale.scale_id,
        name: scale.scale_name,
        totalTimeMs: scale.total_time_ms,
        reasonForExit: scale.reason_for_exit,
        items: (items ?? [])
          .filter((item) => item.scale_id === scale.id)
          .map((item) => ({
            id: item.item_id,
            name: item.name,
            seen: item.seen,
            score: item.score,
            answer: item.answer,
            skipped: item.skipped,
            practice: item.practice,
            timeTakenMs: item.time_taken_ms,
          })),
      })),
    };

    return NextResponse.json(reconstructed, { status: 200 });
  } catch (error) {
    console.error("Fetch error:", error);
    return NextResponse.json({ message: "error", error }, { status: 500 });
  }
}
