import { NextRequest, NextResponse } from "next/server";

import { AssessmentResponse } from "@/stores/use-assessment-responses";
import { CreditStatus } from "@/types/admin";
import { ReportVersion } from "@/types/reporting";
import { supabase } from "@/utils/supabase";

interface Student {
  id: string;
  code: string;
  age: number;
  school_id: number;
}

export async function POST(request: NextRequest) {
  try {
    const {
      assessment,
      student,
      completedAt,
      assessmentResultId,
    }: {
      assessment: AssessmentResponse;
      student: Student;
      completedAt?: string;
      assessmentResultId?: string;
    } = await request.json();

    if (!assessment || !student) {
      return NextResponse.json(
        { error: "Assessment and student data are required" },
        { status: 400 }
      );
    }

    const {
      name,
      assessmentType,
      assessmentAudience,
      ...cleanedAssessmentData
    } = assessment;

    const host = request.headers.get("host") || "";
    const vercel_branch_url = host.includes("localhost")
      ? "dev.talamo.co.uk"
      : host;

    const payload = {
      user_id: student.id,
      student_code: student.code,
      age: student.age,
      env: process.env.NODE_ENV === "production" ? "prod" : "dev",
      school_id: student.school_id,
      date_taken: completedAt || new Date().toISOString(),
      assessment_id: assessment.id,
      assessment_name: name,
      assessment_data_id: assessmentResultId,
      vercel_branch_url: vercel_branch_url,
      assessment_data: cleanedAssessmentData,
      studentAge: student.age,
      studentCode: student.code,
      userID: student.id,
      version: ReportVersion.V2,
    };

    try {
      const response = await fetch(process.env.SCHOOL_DATA_ANALYSIS_ENDPOINT!, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.SCHOOL_API_KEY_DATA_EXPORT_API!,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        console.error(
          "Failed to send to analysis endpoint:",
          response.statusText
        );
        return NextResponse.json(
          {
            success: false,
            message: "Failed to send to analysis endpoint",
            error: response.statusText,
          },
          { status: 500 }
        );
      }

      try {
        await supabase
          .from("credits")
          .update({
            status: CreditStatus.Redeemed,
            redeemed_at: new Date().toISOString(),
          })
          .eq("student_id", parseInt(student.id))
          .eq("assessment_id", assessment.id);
      } catch (creditError) {
        console.error("Failed to redeem credit:", creditError);
      }

      return NextResponse.json({
        success: true,
        message: "Assessment data sent successfully",
        data: payload,
      });
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          message: "Error sending to analysis endpoint",
          error: error instanceof Error ? error.message : "Unknown error",
        },
        { status: 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
