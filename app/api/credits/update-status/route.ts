import { NextRequest, NextResponse } from "next/server";

import { CreditStatus } from "@/types/admin";
import { supabase } from "@/utils/supabase";

interface UpdateCreditStatusRequest {
  student_id: number;
  assessment_id: string;
  status: CreditStatus;
}

export async function POST(request: NextRequest) {
  try {
    const { student_id, assessment_id, status }: UpdateCreditStatusRequest =
      await request.json();

    if (!student_id || !assessment_id || !status) {
      return NextResponse.json(
        { error: "student_id, assessment_id, and status are required" },
        { status: 400 }
      );
    }

    if (!Object.values(CreditStatus).includes(status)) {
      return NextResponse.json(
        { error: "Invalid status value" },
        { status: 400 }
      );
    }

    const updateData: { status: CreditStatus; redeemed_at?: string } = {
      status,
    };

    if (status === CreditStatus.Redeemed) {
      updateData.redeemed_at = new Date().toISOString();
    }

    const { data: updatedCredit, error } = await supabase
      .from("credits")
      .update(updateData)
      .eq("student_id", student_id)
      .eq("assessment_id", assessment_id)
      .select()
      .maybeSingle();

    if (error) {
      return NextResponse.json(
        { error: "Error updating credit status", details: error },
        { status: 500 }
      );
    }

    if (!updatedCredit) {
      return NextResponse.json(
        { error: "Credit not found for student for this assessment" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      credit: updatedCredit,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
