import { NextRequest, NextResponse } from "next/server";

import { supabase } from "@/utils/supabase";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const student_id = searchParams.get("student_id");
    const assessment_id = searchParams.get("assessment_id");

    if (!student_id || !assessment_id) {
      return NextResponse.json(
        { error: "student_id and assessment_id are required" },
        { status: 400 }
      );
    }

    const { data: credit, error } = await supabase
      .from("credits")
      .select("id, status, assessment_id, redeemed_at")
      .eq("student_id", parseInt(student_id))
      .eq("assessment_id", assessment_id)
      .maybeSingle();

    if (error) {
      return NextResponse.json(
        { error: "Error fetching credit", details: error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      hasCredit: !!credit,
      credit: credit || null,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
