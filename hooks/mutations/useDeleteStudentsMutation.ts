import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { CreditStatus } from "@/types/admin";
import { supabase } from "@/utils/supabase";

interface deleteStudentsInput {
  studentIds: number[];
}

export const deleteStudents = async ({ studentIds }: deleteStudentsInput) => {
  const { data: updateData, error: updateError } = await supabase
    .from("student")
    .update({ deactivated: true })
    .in("student_id", studentIds)
    .select("student_code");

  const { error: updateCreditError } = await supabase
    .from("credits")
    .update({
      status: CreditStatus.Unassigned,
      assessment_id: null,
      student_id: null,
    })
    .in("student_id", studentIds)
    .not("status", "eq", CreditStatus.Redeemed)
    .select("student_id");

  if (updateError) {
    throw new Error(updateError.message);
  }

  if (updateCreditError) {
    throw new Error(updateCreditError.message);
  }

  return {
    studentCodes: updateData,
  };
};

export const useDeleteStudentsMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["DeleteStudents"],
    mutationFn: deleteStudents,
    onSuccess: () => {
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === CACHE_KEYS.STUDENTS_BY_SCHOOL ||
          query.queryKey[0] === CACHE_KEYS.CREDITS_BY_SCHOOL,
      });
    },
  });
};
