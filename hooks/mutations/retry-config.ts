export const MUTATION_RETRY_CONFIG = {
  standard: {
    retry: 3,
    retryDelay: (attempt: number) => Math.min(1000 * 2 ** attempt, 5000),
  },

  critical: {
    retry: 5,
    retryDelay: (attempt: number) => Math.min(1000 * 2 ** attempt, 10000),
  },

  background: {
    retry: 6,
    retryDelay: (attempt: number) => Math.min(1500 * 2 ** attempt, 15000),
    gcTime: 1000 * 60 * 5,
    networkMode: "always" as const,
  },
} as const;
