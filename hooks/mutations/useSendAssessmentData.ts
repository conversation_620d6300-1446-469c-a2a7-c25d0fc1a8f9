import { useMutation } from "@tanstack/react-query";
import { useCallback, useState } from "react";

import { Student } from "@/context/student-context";
import { AssessmentResponse } from "@/stores/use-assessment-responses";

import { MUTATION_RETRY_CONFIG } from "./retry-config";

interface SendAssessmentDataInput {
  assessment: AssessmentResponse;
  student: Student;
  completedAt?: string;
  assessmentResultId?: string | null;
}

interface SendAssessmentDataPayload {
  user_id: string;
  student_code: string;
  age: number;
  env: string;
  school_id: number;
  date_taken: string;
  assessment_id: string;
  assessment_name: string;
  assessment_data_id: string | null;
  vercel_branch_url: string;
  assessment_data: Omit<
    AssessmentResponse,
    "name" | "assessmentType" | "assessmentAudience"
  >;
  studentAge: number;
  studentCode: string;
  userID: string;
  version: string;
}

interface SendAssessmentDataResponse {
  success: true;
  message: string;
  data: SendAssessmentDataPayload;
}

export const useSendAssessmentData = () => {
  const [showRetryFailedModal, setShowRetryFailedModal] = useState(false);

  const mutation = useMutation<
    SendAssessmentDataResponse,
    Error,
    SendAssessmentDataInput
  >({
    mutationFn: async (data) => {
      const res = await fetch("/api/assessment/send-assessment-data", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!res.ok) {
        const errorData = await res
          .json()
          .catch(() => ({ error: "Unknown error" }));
        throw new Error(errorData.error || "Failed to send assessment data");
      }
      return res.json();
    },
    ...MUTATION_RETRY_CONFIG.background,
    onError: (error) => {
      console.error("Failed to send assessment data after retries:", error);
      setShowRetryFailedModal(true);
    },
  });

  const sendAssessmentDataInBackground = useCallback(
    (data: SendAssessmentDataInput) => {
      return mutation.mutateAsync(data).catch((error) => {
        console.error("Background assessment data send failed:", error);
        throw error;
      });
    },
    [mutation]
  );

  return {
    sendAssessmentData: sendAssessmentDataInBackground,
    mutation,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    showRetryFailedModal,
    setShowRetryFailedModal,
    retry: () => mutation.reset(),
  };
};
