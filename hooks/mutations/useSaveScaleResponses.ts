import { useMutation } from "@tanstack/react-query";
import { useState } from "react";

import { AssessmentResponse } from "@/stores/use-assessment-responses";

import { MUTATION_RETRY_CONFIG } from "./retry-config";

interface SaveScalePayload {
  assessment_id?: string;
  student_id: string;
  student_code: string;
  assessment_response?: AssessmentResponse | null;
}

interface SaveScaleResponse {
  message: string;
  assessmentResultId: string;
}

export const useSaveScaleResponses = () => {
  const [showRetryFailedModal, setShowRetryFailedModal] = useState(false);

  const mutation = useMutation<SaveScaleResponse, Error, SaveScalePayload>({
    mutationFn: async (data) => {
      const res = await fetch("/api/assessment/save-scale-responses", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!res.ok) throw new Error("Failed to save scale responses");
      return res.json();
    },
    ...MUTATION_RETRY_CONFIG.critical,
    onError: () => {
      setShowRetryFailedModal(true);
    },
  });

  return {
    saveScaleResponses: mutation,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    showRetryFailedModal,
    setShowRetryFailedModal,
  };
};
