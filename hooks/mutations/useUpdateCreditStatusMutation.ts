import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { CreditStatus } from "@/types/admin";

interface UpdateCreditStatusInput {
  student_id: number;
  assessment_id: string;
  status: CreditStatus;
}

interface UpdateCreditStatusResponse {
  success: boolean;
  credit: {
    id: number;
    student_id: number;
    assessment_id: string;
    status: string;
    redeemed_at?: string;
  };
}

export const updateCreditStatus = async (
  data: UpdateCreditStatusInput
): Promise<UpdateCreditStatusResponse> => {
  const res = await fetch("/api/credits/update-status", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    const errorData = await res
      .json()
      .catch(() => ({ error: "Unknown error" }));
    throw new Error(errorData.error || "Failed to update credit status");
  }

  return res.json();
};

export const useUpdateCreditStatusMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateCreditStatus,
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: [
          CACHE_KEYS.CREDITS,
          variables.student_id,
          variables.assessment_id,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEYS.CREDITS],
      });
    },
  });
};
