import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";

export const addStudents = async ({ students }) => {
  const response = await fetch("/api/add-students", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ students }),
  });

  if (!response.ok) {
    throw new Error("Network response was not ok");
  }

  return response.json();
};

export const useAddStudentsMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["AddStudents"],
    mutationFn: addStudents,
    onSuccess: () => {
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === CACHE_KEYS.STUDENTS_BY_SCHOOL ||
          query.queryKey[0] === CACHE_KEYS.CREDITS_BY_SCHOOL,
      });
    },
  });
};
