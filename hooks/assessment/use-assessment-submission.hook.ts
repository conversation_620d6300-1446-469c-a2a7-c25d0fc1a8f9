import { useContext } from "react";

import { StudentContext } from "@/context/student-context";
import { useAssessment } from "@/hooks/assessment/use-assessment.hook";
import { useSaveScaleResponses } from "@/hooks/mutations/useSaveScaleResponses";
import { useSendAssessmentData } from "@/hooks/mutations/useSendAssessmentData";
import { useAssessmentResponsesStore } from "@/stores/use-assessment-responses";
import { useTimeTakenStore } from "@/stores/use-time-taken-store";

interface AssessmentSubmissionParams {
  selectedResponses: string[];
  isCorrect: boolean | null;
  skipped: boolean;
  reasonForExit?: "kickout" | "timeout" | "finished";
}

export const useAssessmentSubmission = ({
  assessmentData,
  scaleId,
  scaleItemId,
  filteredScaleItemIds,
}: {
  assessmentData: any;
  scaleId: string;
  scaleItemId: string;
  filteredScaleItemIds: string[];
}) => {
  const { student } = useContext(StudentContext);

  const timeTakenMs = useTimeTakenStore((s) => s.elapsedTimeMs);

  const { currentScale, currentScaleItem } = useAssessment({
    assessmentData,
    scaleId,
    scaleItemId,
  });

  const { saveScaleResponses, showRetryFailedModal, setShowRetryFailedModal } =
    useSaveScaleResponses();

  const { sendAssessmentData } = useSendAssessmentData();

  const allScaleIds = assessmentData.assessmentContent
    .filter(
      (
        content
      ): content is Extract<
        (typeof assessmentData.assessmentContent)[number],
        { __typename: "NovaScaleRecord" }
      > => content.__typename === "NovaScaleRecord"
    )
    .map((scale) => scale.id);

  const currentItemIndex = filteredScaleItemIds.findIndex(
    (id) => id === scaleItemId
  );
  const isLastItemInScale =
    currentItemIndex === filteredScaleItemIds.length - 1;

  const submit = async ({
    selectedResponses,
    isCorrect,
    skipped,
    reasonForExit,
  }: AssessmentSubmissionParams): Promise<{
    networkError: boolean;
    success: boolean;
  }> => {
    if (!currentScale || !currentScaleItem)
      return { networkError: true, success: false };

    let networkError = false;
    let success = false;

    const store = useAssessmentResponsesStore.getState();
    const hasSubmitted = selectedResponses.length > 0;

    store.addItemResponse({
      scaleId: currentScale.id,
      scaleName: currentScale.displayName,
      item: {
        id: currentScaleItem.id,
        name: currentScaleItem.internalId,
        practice: currentScaleItem.practiceItem ?? false,
        seen: true,
        skipped: skipped,
        timeTakenMs,
        score: hasSubmitted ? (isCorrect ? 1 : 0) : null,
        answer: selectedResponses,
      },
    });

    if (
      isLastItemInScale ||
      reasonForExit === "timeout" ||
      reasonForExit === "kickout"
    ) {
      store.completeScale({
        scaleId: currentScale.id,
        reasonForExit: reasonForExit ?? "finished",
      });
    }

    //Assessment completion logic
    const store_assessment = store.getAssessment();
    const completedScales = store_assessment?.scales?.length || 0;
    const totalScales = allScaleIds.length;
    const isAssessmentComplete = completedScales === totalScales;

    if (
      isAssessmentComplete &&
      (isLastItemInScale ||
        reasonForExit === "timeout" ||
        reasonForExit === "kickout")
    ) {
      const assessment = store.getAssessment();
      const totalTimeMs =
        assessment?.scales.reduce(
          (sum, scale) =>
            sum +
            scale.items.reduce(
              (itemSum, item) => itemSum + item.timeTakenMs,
              0
            ),
          0
        ) ?? 0;

      store.completeAssessment({
        dateCompleted: new Date().toISOString(),
        totalTimeMs,
      });

      if (student && assessment?.id) {
        const savePromise = saveScaleResponses.mutateAsync({
          assessment_id: assessment?.id,
          student_id: student.id,
          student_code: student.code,
          assessment_response: assessment,
        });

        savePromise
          .then((saveResult) => {
            return sendAssessmentData({
              assessment,
              student,
              completedAt: new Date().toISOString(),
              assessmentResultId: saveResult?.assessmentResultId,
            });
          })
          .catch((saveError) => {
            console.error(
              "Background final assessment save failed:",
              saveError
            );
            return sendAssessmentData({
              assessment,
              student,
              completedAt: new Date().toISOString(),
              assessmentResultId: null,
            });
          });
      }
    }

    try {
      const assessment = store.getAssessment();
      const shouldSaveScaleToDb =
        isLastItemInScale ||
        reasonForExit === "timeout" ||
        reasonForExit === "kickout";

      if (student && shouldSaveScaleToDb) {
        saveScaleResponses
          .mutateAsync({
            assessment_id: assessment?.id,
            student_id: student.id,
            student_code: student.code,
            assessment_response: assessment,
          })
          .catch((error) => {
            console.error("Background scale save failed:", error);
          });
      }
      success = true;
    } catch (err) {
      console.error("Failed to save scale", err);
      networkError = true;
      success = false;
    }

    return { networkError, success };
  };

  return {
    submit,
    showRetryFailedModal,
    setShowRetryFailedModal,
  };
};
