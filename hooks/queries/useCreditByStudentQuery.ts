import { useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";

export interface CreditCheck {
  hasCredit: boolean;
  credit: {
    id: number;
    status: string;
    assessment_id: string;
    redeemed_at?: string;
  } | null;
}

export const fetchCreditByStudent = async (
  student_id: number,
  assessment_id: string
): Promise<CreditCheck> => {
  const params = new URLSearchParams({
    student_id: student_id.toString(),
    assessment_id,
  });

  const res = await fetch(`/api/credits/check?${params}`);

  if (!res.ok) {
    throw new Error("Failed to fetch credit");
  }

  return res.json();
};

export const useCreditByStudentQuery = ({
  student_id,
  assessment_id,
  enabled = true,
}: {
  student_id: number;
  assessment_id: string;
  enabled?: boolean;
}) => {
  return useQuery({
    queryKey: [CACHE_KEYS.CREDITS, student_id, assessment_id],
    queryFn: () => fetchCreditByStudent(student_id, assessment_id),
    enabled: enabled && !!student_id && !!assessment_id,
  });
};
