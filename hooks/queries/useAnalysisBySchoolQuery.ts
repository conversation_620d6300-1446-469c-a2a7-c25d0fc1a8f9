import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { AnalysisData } from "@/types/analysis";
import { supabase } from "@/utils/supabase";

interface FetchStudentBySchoolIdInput {
  schoolId?: number;
}

export const fetchAnalysisBySchoolId = async ({
  schoolId,
  page = 0,
  pageSize = 100,
}: {
  schoolId?: number;
  page?: number;
  pageSize?: number;
}) => {
  const from = page * pageSize;
  const to = from + pageSize - 1;

  const { data, error, count } = await supabase
    .from("assessment_results")
    .select(
      `id,
        analysis,
        result_data,
        student_code,
        assessment_meta,
        hide_report,
        student:student_code (
          first_names,
          surname, 
          year
        )
      `,
      { count: "exact" }
    )
    .eq("school_id", schoolId)
    .order("created_at", { ascending: false })
    .order("id", { ascending: false })
    .range(from, to);

  if (error) {
    throw new Error(error.message);
  }

  const typedData = (data || []).map((item: any) => ({
    ...item,
    student: item.student ? item.student : null,
  })) as AnalysisData[];

  const sortedData = typedData?.sort((a, b) =>
    a.student.surname.localeCompare(b.student.surname)
  );

  return {
    data: sortedData,
    totalCount: count ?? 0,
  };
};

export const useAnalysisBySchoolQuery = ({
  schoolId,
}: FetchStudentBySchoolIdInput) => {
  return useQuery({
    queryKey: [CACHE_KEYS.ANALYSIS_BY_SCHOOL, schoolId],
    queryFn: () =>
      fetchAnalysisBySchoolId({ schoolId, page: 0, pageSize: 1000 }),
    enabled: !!schoolId,
  });
};

export const useInfiniteAnalysisBySchoolQuery = ({
  schoolId,
  pageSize = 100,
}: {
  schoolId?: number;
  pageSize?: number;
}) => {
  return useInfiniteQuery({
    queryKey: [CACHE_KEYS.ANALYSIS_BY_SCHOOL, "infinite", schoolId, pageSize],
    queryFn: ({ pageParam = 0 }) =>
      fetchAnalysisBySchoolId({ schoolId, page: pageParam, pageSize }),
    initialPageParam: 0,
    getNextPageParam: (lastPage, allPages) => {
      const totalLoaded = allPages.reduce(
        (sum, page) => sum + page.data.length,
        0
      );
      return totalLoaded < lastPage.totalCount ? allPages.length : undefined;
    },
    enabled: !!schoolId,
  });
};
