import { useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { FeedbackData } from "@/types/analysis";
import { supabase } from "@/utils/supabase";

interface FetchByReportIdInput {
  reportId?: string;
}

export const getFeedbackByReportId = async ({
  reportId,
}: FetchByReportIdInput): Promise<FeedbackData | null> => {
  const { data, error } = await supabase
    .from("assessment_results")
    .select(
      `id,
       recommendations_feedback
      `
    )
    .eq("id", reportId)
    .single();

  if (error && error.code !== "PGRST116") {
    throw new Error(error.message);
  }

  if (!data) {
    return null;
  }

  const feedbackData: FeedbackData = {
    id: data.id,
    recommendations_feedback: data.recommendations_feedback,
  };

  return feedbackData;
};

export const useGetFeedbackByReportIdQuery = ({
  reportId,
}: FetchByReportIdInput) => {
  return useQuery({
    queryKey: [CACHE_KEYS.FEEDBACK_BY_REPORT_ID, reportId],
    queryFn: () => getFeedbackByReportId({ reportId }),
    enabled: !!reportId,
  });
};
