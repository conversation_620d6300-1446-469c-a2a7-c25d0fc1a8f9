query GetNovaAssessment($id: ItemId!) {
  novaAssessment(filter: { id: { eq: $id } }) {
    __typename
    id
    displayName
    assessmentType
    audience
    shuffle
    nudge
    nudgeTimerDurationS
    tutorialVideo {
      url
    }
    assessmentContent: content {
      __typename
      ... on NovaScaleRecord {
        id
        internalId
        displayName
        icon {
          url
          responsiveImage {
            ...ResponsiveImageFields
          }
        }
        disabledIcon {
          url
          responsiveImage {
            ...ResponsiveImageFields
          }
        }
        scaleTimer
        scaleTimerDuration
        nudge
        nudgeTimerDuration
        kickOut
        kickOutThreshold
        countdownAnimation {
          url
        }
        completeAnimation {
          url
        }
        scaleContent: content {
          __typename
          ... on NovaScaleItemRecord {
            id
            internalId
            relevantAges
            practiceItem
            feedback
            skippable
            disabledWhileAutoplay
            tutorial
            stimulusType
            stimulusText
            stimulusMedia {
              url
            }
            audioPlays
            responseType
            responseMedia {
              id
              url
              title
              filename
              smallImage: responsiveImage(
                imgixParams: { w: "136", h: "136", q: 60 }
              ) {
                ...ResponsiveImageFields
              }
              mediumImage: responsiveImage(
                imgixParams: { w: "176", h: "176", q: 60 }
              ) {
                ...ResponsiveImageFields
              }
            }
            responseText
            gridColumns
            minimumResponsesRequired
            maximumResponsesAllowed
            correctAnswer
            ifCorrectText
            ifCorrectAudio {
              url
            }
            ifCorrectImage {
              responsiveImage {
                ...ResponsiveImageFields
              }
            }
            ifIncorrectAction
            ifIncorrectText
            ifIncorrectAudio {
              url
            }
            ifIncorrectImage {
              responsiveImage {
                ...ResponsiveImageFields
              }
            }
            tutorialText
            tutorialAudio {
              url
            }
            tutorialImage {
              url
              responsiveImage {
                ...ResponsiveImageFields
              }
            }
          }
        }
      }
    }
    icon {
      url
    }
  }
}
