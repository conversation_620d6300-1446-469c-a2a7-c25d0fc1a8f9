"use server";

import { createClient } from "@supabase/supabase-js";

import { supabaseConfig } from "@/data/constants";
import { GetNovaAssessmentQuery } from "@/types/graphql/graphql";

const shuffleArray = <T>(arr: T[]): T[] => {
  const copy = [...arr];
  for (let i = copy.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [copy[i], copy[j]] = [copy[j], copy[i]];
  }
  return copy;
};

const reorderAssessmentContent = (
  assessment: GetNovaAssessmentQuery["novaAssessment"],
  order: string[]
): GetNovaAssessmentQuery["novaAssessment"] => {
  if (!assessment) return null;
  const scales =
    assessment.assessmentContent?.filter(
      (item) => item.__typename === "NovaScaleRecord"
    ) || [];
  const others =
    assessment.assessmentContent?.filter(
      (item) => item.__typename !== "NovaScaleRecord"
    ) || [];

  const map = new Map(scales.map((s) => [s.id, s]));
  const orderedScales = order
    .map((id) => map.get(id))
    .filter((item): item is NonNullable<typeof item> => item !== undefined);

  return {
    ...assessment,
    __typename: "NovaAssessmentRecord",
    assessmentContent: [...orderedScales, ...others],
  };
};

export const getShuffledAssessment = async (
  assessment: GetNovaAssessmentQuery["novaAssessment"] | null | undefined,
  sessionId: string
): Promise<GetNovaAssessmentQuery["novaAssessment"] | null> => {
  if (!assessment) return null;
  if (!assessment.shuffle) {
    return assessment;
  }

  const supabase = createClient(
    supabaseConfig.supabaseUrl,
    supabaseConfig.supabaseKey
  );

  const { data } = await supabase
    .from("assessment_scale_ordering")
    .select("scale_order")
    .eq("session_id", sessionId)
    .eq("assessment_id", assessment.id)
    .maybeSingle();

  let order: string[] = data?.scale_order;

  if (!order?.length) {
    order = shuffleArray(
      assessment.assessmentContent
        .filter((c) => c.__typename === "NovaScaleRecord")
        .map((c) => c.id)
    );

    const { error: insertError } = await supabase
      .from("assessment_scale_ordering")
      .insert({
        session_id: sessionId,
        assessment_id: assessment.id,
        scale_order: order,
      });

    if (insertError) {
      console.error(
        "Supabase insert error:",
        insertError.message,
        insertError.details
      );
    }
  }

  return reorderAssessmentContent(assessment, order);
};
