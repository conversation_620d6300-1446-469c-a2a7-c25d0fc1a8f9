"use client";

import { XMarkIcon } from "@heroicons/react/24/outline";
import <PERSON><PERSON> from "lottie-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useRef, useState } from "react";

import { Button, NightSkyBg } from "@/components/ui";
import { StudentContext } from "@/context/student-context";
import { useInactivityNudge } from "@/hooks/assessment/use-inactivity-nudge";
import { useCreditByStudentQuery } from "@/hooks/queries/useCreditByStudentQuery";
import { cn } from "@/lib/utils";
import CompleteCheckAnimation from "@/lottie/complete-check.json";
import { useAssessmentResponsesStore } from "@/stores/use-assessment-responses";
import { useAudioStore } from "@/stores/use-audio-store";
import { GetNovaAssessmentQuery } from "@/types/graphql/graphql";

import { AssessmentExitModal } from "../modals/assessment-exit-modal";
import { InactivityNudgeModal } from "../modals/inactivity-nudge-modal";

interface AssessmentDashboardProps {
  assessmentId: string;
  assessmentData: GetNovaAssessmentQuery["novaAssessment"];
}

export const AssessmentDashboard = ({
  assessmentId,
  assessmentData,
}: AssessmentDashboardProps) => {
  const { logout, student } = useContext(StudentContext);
  const router = useRouter();
  const [hasMounted, setHasMounted] = useState(false);
  const [exitModalOpen, setExitModalOpen] = useState(false);
  const [completedScaleIds, setCompletedScaleIds] = useState<string[]>([]);
  const [justCompletedScaleId, setJustCompletedScaleId] = useState<
    string | null
  >(null);
  const previousCompletedRef = useRef<string[]>([]);
  const { stop: stopAudio } = useAudioStore();
  const { shouldShowModal, cancel } = useInactivityNudge(
    assessmentData?.nudge ?? false,
    assessmentData?.nudgeTimerDurationS ?? 60
  );

  const { data: creditCheck } = useCreditByStudentQuery({
    student_id: student?.id ? parseInt(student.id) : 0,
    assessment_id: assessmentId,
    enabled: !!student?.id,
  });

  useEffect(() => {
    setHasMounted(true);
    stopAudio();
  }, []);

  useEffect(() => {
    if (creditCheck) {
      if (!creditCheck.hasCredit || creditCheck.credit?.status === "Redeemed") {
        router.push("/dashboard");
      }
    }
  }, [creditCheck, router]);

  useEffect(() => {
    if (!hasMounted) return;

    const assessment = useAssessmentResponsesStore.getState().getAssessment();
    const currentCompleted = assessment?.scales?.map((s) => s.id) ?? [];

    const newlyCompleted = currentCompleted.filter(
      (id) => !previousCompletedRef.current.includes(id)
    );
    if (newlyCompleted.length > 0) {
      setJustCompletedScaleId(newlyCompleted[newlyCompleted.length - 1]);
      setTimeout(() => setJustCompletedScaleId(null), 2000);
    }

    previousCompletedRef.current = currentCompleted;
    setCompletedScaleIds(currentCompleted);
  }, [hasMounted]);

  if (!hasMounted) return null;

  const assessmentScales =
    assessmentData?.assessmentContent.filter(
      (content) => content.__typename === "NovaScaleRecord"
    ) ?? [];

  const totalScales = assessmentScales.length;
  const totalCompleted = completedScaleIds.length;

  const completedStatus: "none" | "partial" | "complete" =
    totalCompleted === 0
      ? "none"
      : totalCompleted < totalScales
        ? "partial"
        : "complete";

  const firstIncompleteScale = assessmentScales.find(
    (scale) => !completedScaleIds.includes(scale.id)
  );

  return (
    <>
      <div className="w-full h-full flex flex-col justify-center items-center gap-28">
        <div className="grid gap-4 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
          {assessmentScales &&
            assessmentScales.map((scale) => {
              const isCompleted = completedScaleIds.includes(scale.id);
              const isFirstScale = assessmentScales[0]?.id === scale.id;
              const isEnabled =
                (completedStatus === "none" && isFirstScale) ||
                scale.id === firstIncompleteScale?.id;

              return (
                <div key={scale.id}>
                  <Link
                    href={
                      isEnabled
                        ? `/assessment/${assessmentId}/scale/${scale.id}`
                        : "#"
                    }
                    aria-disabled={!isEnabled}
                    className={cn(
                      "relative flex flex-col justify-center items-center bg-white rounded-3xl gap-3 w-[194px] h-[185px]",
                      !isEnabled &&
                        !isCompleted &&
                        "pointer-events-none bg-white/50"
                    )}
                  >
                    {isCompleted ? (
                      <div className="w-[96px] h-[96px] relative">
                        <Lottie
                          animationData={CompleteCheckAnimation}
                          loop={false}
                          autoplay={justCompletedScaleId === scale.id}
                          className={cn(
                            "absolute w-[140px] h-[140px] block top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                          )}
                          initialSegment={
                            justCompletedScaleId === scale.id
                              ? undefined
                              : [90, 90]
                          }
                        />
                      </div>
                    ) : (
                      <Image
                        src={scale.disabledIcon?.url ?? scale.icon.url}
                        width={96}
                        height={96}
                        alt={scale.displayName ?? ""}
                        className={!isEnabled ? "grayscale opacity-50" : ""}
                      />
                    )}
                    <div className="text-center text-2xl font-semibold text-black">
                      {scale.displayName}
                    </div>
                  </Link>
                </div>
              );
            })}
        </div>
        <div className="-mb-28">
          {completedStatus === "complete" ? (
            <Link href="/dashboard">
              <Button size="xl">Finish</Button>
            </Link>
          ) : completedStatus === "none" ? (
            <Link
              href={`/assessment/${assessmentId}/${firstIncompleteScale?.id}`}
            >
              <Button size="xl">Start</Button>
            </Link>
          ) : (
            <Link
              href={`/assessment/${assessmentId}/scale/${firstIncompleteScale?.id}`}
            >
              <Button size="xl">Continue</Button>
            </Link>
          )}
        </div>
      </div>

      <NightSkyBg />
      <div className="fixed top-5 left-5">
        <Button
          size="icon"
          variant="negative"
          onClick={() => setExitModalOpen(true)}
        >
          <XMarkIcon width={24} height={24} strokeWidth={2.5} />
        </Button>
      </div>
      <AssessmentExitModal
        open={exitModalOpen}
        onOpenChange={setExitModalOpen}
      />
      <InactivityNudgeModal
        show={shouldShowModal}
        onCancel={cancel}
        onLogout={logout}
        variant="dashboard"
      />
    </>
  );
};
