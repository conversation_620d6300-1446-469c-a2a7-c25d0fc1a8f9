"use client";

import "@/styles/globals.css";

import { Box, Spinner } from "@chakra-ui/react";
import { createColumnHelper, SortingState } from "@tanstack/react-table";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";

import { ScoreLevel } from "@/components/ScoreLevel/ScoreLevel";
import { ScoreNumber } from "@/components/ScoreNumber/ScoreNumber";
import { But<PERSON>, Panel } from "@/components/v2";
import { Text } from "@/components/v2/Text/Text";
import { useSchoolUser } from "@/context/school-context";
import { useInfiniteAnalysisBySchoolQuery } from "@/hooks/queries/useAnalysisBySchoolQuery";
import { theme } from "@/styles/theme";
import { AnalysisData } from "@/types/analysis";

import { DataTable } from "../DataTable/DataTable";
import { PDFDownloadMenu } from "../PDFMenu/PDFMenu";

const PAGE_SIZE = 100;

export const AnalysisTable = () => {
  const { user } = useSchoolUser();
  const [sorting, setSorting] = useState<SortingState>([
    { id: "student.surname", desc: true },
  ]);
  const schoolId = user?.orgId || 0;
  const [rowSelection, setRowSelection] = useState({});
  const router = useRouter();

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: analysisLoading,
  } = useInfiniteAnalysisBySchoolQuery({
    schoolId: schoolId,
    pageSize: PAGE_SIZE,
  });

  const analysisData = useMemo(() => {
    return data?.pages.flatMap((page) => page.data) ?? [];
  }, [data?.pages]);

  const totalCount = data?.pages[0]?.totalCount ?? 0;

  const columnHelper = createColumnHelper<AnalysisData>();

  const renderCellContent = (
    info: any,
    value: string | JSX.Element | number
  ): string | JSX.Element | number => {
    const row = info.row.original;
    if (row.hide_report) {
      return "-";
    }
    return value;
  };

  const hideReportSortingFn = (rowA, rowB, columnId) => {
    const hideReportA = rowA.original.hide_report;
    const hideReportB = rowB.original.hide_report;

    const desc = sorting[0]?.desc;

    if (hideReportA !== hideReportB) {
      return desc ? (hideReportA ? -1 : 1) : hideReportA ? 1 : -1;
    }

    const valueA = rowA.getValue(columnId);
    const valueB = rowB.getValue(columnId);

    if (typeof valueA === "string" && typeof valueB === "string") {
      return desc ? valueB.localeCompare(valueA) : valueB.localeCompare(valueA);
    }

    if (typeof valueA === "number" && typeof valueB === "number") {
      return desc ? valueB - valueA : valueB - valueA;
    }

    return 0;
  };

  const columns = useMemo(
    () => [
      columnHelper.accessor("student.surname", {
        id: "student.surname",
        cell: ({ row }) => {
          const name = `${
            row.original.student.surname
          }, ${row.original.student.first_names.charAt(0)}`;
          return (
            <Link
              href={`/school/analysis/report/${row.original.student_code}`}
              className="blueLink"
            >
              <Text
                color={theme.colors.ui.link.hex}
                variant="sm"
                fontWeight={600}
                cursor="pointer"
                _hover={{ textDecoration: "underline" }}
              >
                {name}
              </Text>
            </Link>
          );
        },
        header: "Name",
        sortingFn: hideReportSortingFn,
      }),
      columnHelper.accessor("student.year", {
        cell: (info) => info.getValue(),
        header: "Year",
        sortingFn: hideReportSortingFn,
      }),
      columnHelper.accessor("analysis.levelOfNeedRisk", {
        cell: (info) => {
          const row = info.row.original;
          const level = row.hide_report ? 1 : info.getValue();
          return <ScoreLevel level={level} type="text" />;
        },
        header: "Need level",
        sortingFn: hideReportSortingFn,
      }),
      columnHelper.accessor("analysis.dyslexiaRisk", {
        cell: (info) => {
          const row = info.row.original;
          const level = row.hide_report ? 1 : info.getValue();
          return <ScoreLevel level={level} type="arrow" />;
        },
        header: "Dyslexia risk",
        sortingFn: hideReportSortingFn,
      }),
      columnHelper.accessor(
        "result_data.cognitiveProfile.visualReasoning.composite.standardScore",
        {
          cell: (info) =>
            renderCellContent(
              info,
              <ScoreNumber>{info.getValue()}</ScoreNumber>
            ),
          header: "Visual reasoning",
          sortingFn: hideReportSortingFn,
        }
      ),
      columnHelper.accessor(
        "result_data.cognitiveProfile.verbalReasoning.composite.standardScore",
        {
          cell: (info) =>
            renderCellContent(
              info,
              <ScoreNumber>{info.getValue()}</ScoreNumber>
            ),
          header: "Verbal ability",
          sortingFn: hideReportSortingFn,
        }
      ),
      columnHelper.accessor(
        "result_data.cognitiveProfile.phonologicalAwareness.composite.standardScore",
        {
          cell: (info) =>
            renderCellContent(
              info,
              <ScoreNumber>{info.getValue()}</ScoreNumber>
            ),
          header: "Phonolog. awareness",
          sortingFn: hideReportSortingFn,
        }
      ),
      columnHelper.accessor(
        "result_data.cognitiveProfile.processingSpeed.composite.standardScore",
        {
          cell: (info) =>
            renderCellContent(
              info,
              <ScoreNumber>{info.getValue()}</ScoreNumber>
            ),
          header: "Process. speed",
          sortingFn: hideReportSortingFn,
        }
      ),
      columnHelper.accessor(
        "result_data.cognitiveProfile.workingMemory.composite.standardScore",
        {
          cell: (info) =>
            renderCellContent(
              info,
              <ScoreNumber>{info.getValue()}</ScoreNumber>
            ),
          header: "Working memory",
          sortingFn: hideReportSortingFn,
        }
      ),
      columnHelper.accessor(
        "result_data.cognitiveProfile.readingSpeed.composite.standardScore",
        {
          cell: (info) =>
            renderCellContent(
              info,
              <ScoreNumber>{info.getValue()}</ScoreNumber>
            ),
          header: "Reading speed",
          sortingFn: hideReportSortingFn,
        }
      ),
      columnHelper.accessor(
        "result_data.cognitiveProfile.readingComprehension.composite.standardScore",
        {
          cell: (info) =>
            renderCellContent(
              info,
              <ScoreNumber>{info.getValue()}</ScoreNumber>
            ),
          header: "Reading comp.",
          sortingFn: hideReportSortingFn,
        }
      ),
      columnHelper.accessor(
        "result_data.cognitiveProfile.spelling.composite.standardScore",
        {
          cell: (info) =>
            renderCellContent(
              info,
              <ScoreNumber>{info.getValue()}</ScoreNumber>
            ),
          header: "Spelling",
          sortingFn: hideReportSortingFn,
        }
      ),
      columnHelper.accessor("student_code", {
        cell: ({ row }) => {
          const { student_code } = row.original;

          return <PDFDownloadMenu type="icon" studentCode={student_code} />;
        },
        header: "",
      }),
    ],
    []
  );

  const isInitialLoad = analysisLoading && analysisData.length === 0;

  if (isInitialLoad) {
    return <Spinner />;
  }

  if (!data) {
    return null;
  }

  if (analysisData.length === 0) {
    return (
      <Panel preset="panel">
        <Box
          maxW="600px"
          textAlign="center"
          display="flex"
          flexDir="column"
          alignItems="center"
        >
          <Text element="h4" variant="md">
            No results yet!
          </Text>
          <Text element="p" variant="md" mt={theme.spacing.md.px}>
            Once your students start completing the Test, their results will
            show here. You&apos;ll be able to see their scores as well as click
            into their profile and read more about them and how to support them.
          </Text>
          <Box mt={theme.spacing.md.px}>
            <Button size="sm" onClick={() => router.push("/school")}>
              Assign test
            </Button>
          </Box>
        </Box>
      </Panel>
    );
  }

  return (
    <>
      <Box display="flex" h="40px" justifyContent="flex-end"></Box>
      <Box overflow="scroll">
        <Box>
          <DataTable
            columns={columns}
            data={analysisData}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            sorting={sorting}
            setSorting={setSorting}
            hideCheckboxes
          />
          <Box textAlign="center" mt="6">
            <Text fontWeight="bold">
              Showing {analysisData.length}/{totalCount}
            </Text>
            {hasNextPage && (
              <Button
                mt="4"
                onClick={() => fetchNextPage()}
                isLoading={isFetchingNextPage}
                mx="auto"
              >
                Load more
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </>
  );
};
