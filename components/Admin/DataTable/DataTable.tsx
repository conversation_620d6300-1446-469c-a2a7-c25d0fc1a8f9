import {
  Box,
  Checkbox,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from "@chakra-ui/react";
import { ArrowDownIcon, ArrowUpIcon } from "@heroicons/react/24/solid";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  RowSelectionState,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import React from "react";

import { theme } from "@/styles/theme";

export type DataTableProps<Data extends object> = {
  data: Data[];
  columns: ColumnDef<Data, any>[];
  rowSelection: RowSelectionState;
  setRowSelection: React.Dispatch<React.SetStateAction<RowSelectionState>>;
  sorting?: SortingState;
  setSorting?: React.Dispatch<React.SetStateAction<SortingState>>;
  hideCheckboxes?: boolean;
  getRowId?: (originalRow: Data, index: number) => string;
};

const ICON_SIZE = 10;

export const DataTable = <Data extends object>({
  data,
  columns,
  rowSelection,
  setRowSelection,
  sorting,
  setSorting,
  hideCheckboxes = false,
  getRowId,
}: DataTableProps<Data>) => {
  const table = useReactTable({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onRowSelectionChange: setRowSelection,
    enableRowSelection: true,
    enableSortingRemoval: false,
    getRowId,
    state: {
      sorting,
      rowSelection,
    },
  });

  return (
    <Table>
      <Thead>
        {table.getHeaderGroups().map((headerGroup) => (
          <Tr key={headerGroup.id}>
            {!hideCheckboxes && (
              <Th>
                <Checkbox
                  isChecked={table.getIsAllPageRowsSelected()}
                  isIndeterminate={
                    table.getIsSomePageRowsSelected() &&
                    !table.getIsAllPageRowsSelected()
                  }
                  onChange={(e) => {
                    table.toggleAllPageRowsSelected(e.target.checked);
                  }}
                />
              </Th>
            )}
            {headerGroup.headers.map((header) => {
              const meta: any = header.column.columnDef.meta;
              return (
                <Th
                  key={header.id}
                  onClick={header.column.getToggleSortingHandler()}
                  isNumeric={meta?.isNumeric}
                >
                  <Box
                    display="inline-flex"
                    alignItems="center"
                    gap="6px"
                    cursor="pointer"
                    bgColor={
                      header.column.getIsSorted()
                        ? theme.colors.ui.grey_04.hex
                        : undefined
                    }
                    borderRadius="2px"
                    padding="5px"
                    transform="translateX(-6px)"
                  >
                    <Box>
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </Box>
                    <Box flex={1}>
                      {header.column.getIsSorted() ? (
                        header.column.getIsSorted() === "desc" ? (
                          <ArrowDownIcon
                            width={ICON_SIZE}
                            height={ICON_SIZE}
                            aria-label="sorted descending"
                          />
                        ) : (
                          <ArrowUpIcon
                            width={ICON_SIZE}
                            height={ICON_SIZE}
                            aria-label="sorted ascending"
                          />
                        )
                      ) : (
                        <Box
                          width={`${ICON_SIZE}px`}
                          height={`${ICON_SIZE}px`}
                        />
                      )}
                    </Box>
                  </Box>
                </Th>
              );
            })}
          </Tr>
        ))}
      </Thead>
      <Tbody>
        {table.getRowModel().rows.map((row) => (
          <TableRow
            key={row.id}
            row={row as any}
            hideCheckboxes={hideCheckboxes}
            rowSelection={rowSelection}
          />
        ))}
      </Tbody>
    </Table>
  );
};

const TableRow = React.memo(
  <Data extends object>({
    row,
    hideCheckboxes,
    rowSelection,
  }: {
    row: ReturnType<
      ReturnType<typeof useReactTable<Data>>["getRowModel"]
    >["rows"][number];
    hideCheckboxes?: boolean;
    rowSelection: RowSelectionState;
  }) => {
    return (
      <Tr
        key={row.id}
        bg={
          row.getIsSelected() ? theme.colors.secondary.purple_05.hex : "white"
        }
      >
        {!hideCheckboxes && (
          <Td>
            <Checkbox
              isChecked={!!rowSelection[row.id]}
              onChange={(e) => {
                row.toggleSelected(e.target.checked);
              }}
            />
          </Td>
        )}
        {row.getVisibleCells().map((cell) => {
          const meta = cell.column.columnDef.meta as
            | {
                isNumeric?: boolean;
                width?: string;
              }
            | undefined;
          return (
            <Td
              key={cell.id}
              isNumeric={meta?.isNumeric}
              {...(meta?.width ? { maxW: meta.width } : {})}
            >
              {flexRender(cell.column.columnDef.cell, cell.getContext())}
            </Td>
          );
        })}
      </Tr>
    );
  }
);
TableRow.displayName = "TableRow";
