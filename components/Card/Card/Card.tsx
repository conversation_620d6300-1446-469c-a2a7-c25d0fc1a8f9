import {
  Box,
  Card as ChakraCard,
  Checkbox,
  HStack,
  VStack,
} from "@chakra-ui/react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import Link from "next/link";
import router from "next/router";
import React, { useEffect, useReducer, useState } from "react";
import { z } from "zod";

import { Back } from "@/components/Back/Back";
import { Hyperlink } from "@/components/Hyperlink/Hyperlink";
import { InputField } from "@/components/InputField/InputField";
import { Button, Text } from "@/components/v2";
import { theme } from "@/styles/theme";

export type CardProps = {
  children: React.ReactNode;
  showBackButton?: boolean;
  maxWidth?: string;
  title?: string;
};

export const Card = ({
  children,
  maxWidth,
  showBackButton,
  title,
}: CardProps) => {
  return (
    <ChakraCard
      w={{ sm: "100%", md: maxWidth || "464px" }}
      border="none"
      borderRadius={theme.border.radius.md.px}
      bg={theme.colors.primary.white.hex}
      flex={1}
      overflow="hidden"
      variant="outline"
      p={"32px"}
      css={{
        boxShadow: theme.shadow.box,
      }}
    >
      {showBackButton && <Back />}
      {title && (
        <HStack mt={theme.spacing.sm.rem} justifyContent="center">
          <Text
            variant="2xl"
            element="h2"
            mb={theme.spacing.sm.px}
            textAlign="center"
          >
            {title}
          </Text>
        </HStack>
      )}
      {children}
    </ChakraCard>
  );
};

type AuthCardProps = {
  onSubmit: (args?: any) => any;
  errorMsg?: string;
  isSubmitted?: boolean;
};

export const LoginCard = ({ onSubmit, errorMsg }: AuthCardProps) => {
  const [showEmailError, setShowEmailError] = useState(false);
  const [showPasswordError, setShowPasswordError] = useState(false);
  const initialState = { email: "", password: "", isEmailValid: true };
  const actions = {
    SET_PASSWORD: "SET_PASSWORD",
    SET_EMAIL: "SET_EMAIL",
  };

  const reducer = (state, action) => {
    switch (action?.type) {
      case actions.SET_EMAIL:
        const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(
          action?.payload?.value
        );
        return { ...state, email: action?.payload?.value, isEmailValid };
      case actions.SET_PASSWORD:
        return { ...state, password: action?.payload?.value };
      default:
        return state;
    }
  };

  const handleSubmit = () => {
    if (!state.isEmailValid || !state.email || !state.password) {
      setShowEmailError(true);
      if (!state.password) {
        setShowPasswordError(true);
      }

      return;
    }
    setShowEmailError(false);
    setShowPasswordError(false);
    onSubmit({
      email: state.email,
      password: state.password,
    });
  };

  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <>
      <Card title="Login">
        <VStack
          as="form"
          w="100%"
          onSubmit={handleSubmit}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleSubmit();
            }
          }}
        >
          <Text
            element="h3"
            variant="xs"
            textAlign="left"
            alignSelf="flex-start"
            m={0}
          >
            Email
          </Text>
          <InputField
            defaultValue={state?.email}
            onChange={(e) =>
              dispatch({
                type: "SET_EMAIL",
                payload: { value: e.target.value },
              })
            }
            type="email"
            variant="outline"
            placeholder="Email"
          />
          <Text
            element="p"
            variant="md"
            textAlign="left"
            alignSelf="flex-start"
            m={0}
            color={theme.colors.ui.alert_red_01.hex}
          >
            {(!state.isEmailValid || !state?.email.value) &&
              showEmailError &&
              "Please enter a valid email"}
          </Text>
          <Text
            element="h3"
            variant="xs"
            textAlign="left"
            alignSelf="flex-start"
            m={0}
            mt={theme.spacing.sm.px}
          >
            Password
          </Text>
          <InputField
            defaultValue={state?.password}
            onChange={(e) =>
              dispatch({
                type: "SET_PASSWORD",
                payload: { value: e.target.value },
              })
            }
            type="password"
            variant="outline"
            placeholder="Password"
          />
          <Text
            element="p"
            variant="md"
            textAlign="left"
            alignSelf="flex-start"
            m={0}
            color={theme.colors.ui.alert_red_01.hex}
          >
            {errorMsg === "Invalid login credentials"
              ? "Please enter your correct password"
              : errorMsg}

            {showPasswordError &&
              !errorMsg &&
              "Please enter your correct password"}
          </Text>
          <HStack mt={theme.spacing.md.px}>
            <Text>Forgotten password?</Text>
            <Hyperlink href="/login/forgot-password" label="Reset" secondary />
          </HStack>
          <Button
            formAction="submit"
            size="md"
            my={theme.spacing.md.px}
            onClick={handleSubmit}
          >
            Login
          </Button>
          <HStack mt={theme.spacing.sm.px}>
            <Text>Don’t have a login?</Text>
            <Hyperlink
              href="/login/create-account"
              label="Create account"
              secondary
            />
          </HStack>
        </VStack>
      </Card>
      <Box
        textAlign="center"
        mt={theme.spacing.ml.px}
        color={theme.colors.primary.white.hex}
        display="flex"
        gap={theme.spacing.xs.px}
        justifyContent="center"
      >
        If you’re a student{" "}
        <Link href="/student/login">
          <Text
            style={{
              textDecoration: "underline",
              color: theme.colors.primary.white.hex,
              fontWeight: 600,
            }}
          >
            Start here
          </Text>
        </Link>
      </Box>
    </>
  );
};

export const CreateAccountCard = ({ onSubmit }: AuthCardProps) => {
  const [validate, setValidate] = useState(false);
  const [loading, setLoading] = useState(false);
  const initialState = {
    formState: { error: false, clean: true },
    email: { value: "", error: false },
    password: { value: "", error: false },
  };
  const actions = {
    SET_PASSWORD: "SET_PASSWORD",
    SET_EMAIL: "SET_EMAIL",
  };
  const reducer = (state, action) => {
    const emailSchema = z.string().min(5).email();
    const passwordSchema = z.string().min(8);

    switch (action?.type) {
      case actions.SET_EMAIL: {
        let error = false;
        if (!emailSchema.safeParse(action?.payload?.value).success) {
          error = true;
        }
        return {
          ...state,
          formState: { error, clean: false },
          email: { value: action?.payload?.value, error },
        };
      }
      case actions.SET_PASSWORD: {
        let error = false;
        if (!passwordSchema.safeParse(action?.payload?.value).success) {
          error = true;
        }
        return {
          ...state,
          formState: { error, clean: false },
          password: { value: action?.payload?.value, error },
        };
      }
      default:
        return state;
    }
  };

  const [state, dispatch] = useReducer(reducer, initialState);
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [marketingAgreed, setMarketingAgreed] = useState(false);

  const handleCreateAccount = () => {
    setValidate(true);
    const isValid =
      fields.every((field) => !state[field.fieldName].error) && termsAgreed;

    if (isValid) {
      setLoading(true);
      onSubmit({
        email: state?.email?.value,
        password: state?.password?.value,
        isOptedIn: marketingAgreed,
      });
    }
  };

  const fields = [
    {
      id: 1,
      actionType: actions.SET_EMAIL,
      fieldName: "email",
      placeholder: "Email",
      fieldType: "email",
      error: true,
      errorMessage: "Please enter a valid email",
      blankMessage: "Please enter a valid email",
    },
    {
      id: 2,
      actionType: actions.SET_PASSWORD,
      fieldName: "password",
      placeholder: "Password",
      fieldType: "password",
      error: true,
      errorMessage: "Your password must be at least 8 characters",
      blankMessage: "You must enter a password",
    },
  ];
  return (
    <Card title="Create account">
      <VStack as="form" wrap="wrap" gap={theme.spacing.sm.rem} w="100%">
        {fields.map((f) => (
          <>
            <Box
              key={f?.fieldName}
              gap={1}
              justifyContent="space-between"
              w="100%"
            >
              <Text
                element="h3"
                variant="xs"
                textAlign="left"
                alignSelf="flex-start"
                m={0}
              >
                {f?.placeholder}
              </Text>
              <InputField
                defaultValue={state?.[f?.fieldName]?.value}
                onChange={(e) =>
                  dispatch({
                    type: f.actionType,
                    payload: { value: e.target.value },
                  })
                }
                type={f?.fieldType}
                variant="outline"
              />
            </Box>
            {validate && state?.[f.fieldName]?.error && (
              <Text
                element="p"
                variant="md"
                textAlign="left"
                alignSelf="flex-start"
                m={0}
                color={theme.colors.ui.alert_red_01.hex}
              >
                {f.errorMessage}
              </Text>
            )}
            {validate && !state?.[f?.fieldName]?.value && (
              <Text
                element="p"
                variant="md"
                textAlign="left"
                alignSelf="flex-start"
                m={0}
                color={theme.colors.ui.alert_red_01.hex}
              >
                {f.blankMessage}
              </Text>
            )}
          </>
        ))}
        <VStack gap={2} mt={theme.spacing.ml.px}>
          <HStack gap={3}>
            <Checkbox
              colorScheme="purple"
              onChange={() => setMarketingAgreed(!marketingAgreed)}
            />
            <Text element="p" variant="md">
              I would like to receive emails with news, tips and offers from
              Talamo.
            </Text>
          </HStack>
          <HStack gap={3}>
            <Checkbox
              colorScheme="purple"
              onChange={() => setTermsAgreed(!termsAgreed)}
            />
            <Text element="p" variant="md">
              I agree to Talamo’s{" "}
              <Hyperlink
                secondary
                href="http://www.talamo.co.uk/privacy-policy"
                label="Privacy Policy"
                target="_blank"
              />{" "}
              &{" "}
              <Hyperlink
                secondary
                href="http://www.talamo.co.uk/terms-conditions"
                label="Terms & Conditions"
                target="_blank"
              />
            </Text>
          </HStack>
          {validate && !termsAgreed && (
            <Text
              element="p"
              variant="md"
              textAlign="left"
              alignSelf="flex-start"
              m={0}
              color={theme.colors.ui.alert_red_01.hex}
            >
              You must accept the terms & conditions
            </Text>
          )}
        </VStack>
        <Button
          onClick={() => {
            handleCreateAccount();
          }}
        >
          {loading ? "Loading..." : "Sign up"}
        </Button>
        <HStack gap={1} wrap="wrap" mt={theme.spacing.md.rem}>
          <Text element="p" variant="md">{`Already have an account? `}</Text>
          <Hyperlink secondary href="/login" label="Login" />
        </HStack>
      </VStack>
    </Card>
  );
};

export const ForgotPasswordCard = () => {
  const [domain, setDomain] = useState("");
  const [email, setEmail] = useState("");
  const [showError, setShowError] = useState(false);
  const [isSent, setIsSent] = useState(false);
  const [showSubmit, setShowSubmit] = useState(false);
  const validEmailEntered = z.string().email();
  const supabase = createClientComponentClient();
  const redirectUrl = `${domain}/login/reset-password`;

  useEffect(() => {
    setDomain(window.location.origin);
  }, []);

  const handleForgottenPassword = async () => {
    if (!validEmailEntered.safeParse(email).success) {
      setShowError(true);
    } else {
      setShowError(false);
      const { data } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      });

      if (data) {
        setIsSent(true);
      }
    }
  };

  return (
    <Card title={!isSent ? "Reset password" : "Check your email!"}>
      <VStack w="100%">
        {!isSent ? (
          <>
            <Text
              element="p"
              variant="lg"
              textAlign="center"
              mb={theme.spacing.ml.px}
            >
              Enter your email address below. If we find an account using those
              details, we’ll send instructions on how to reset your password.
            </Text>
            <Text
              element="h3"
              variant="xs"
              textAlign="left"
              alignSelf="flex-start"
              m={0}
            >
              Email
            </Text>
            <InputField
              defaultValue={email}
              onChange={(e) => {
                setEmail(e.target.value);
                if (validEmailEntered.safeParse(e.target.value).success) {
                  setShowSubmit(true);
                } else {
                  setShowSubmit(false);
                }
              }}
              type="email"
              variant="outline"
            />
            {showError && (
              <Text
                element="p"
                variant="md"
                textAlign="left"
                alignSelf="flex-start"
                m={0}
                color={theme.colors.ui.alert_red_01.hex}
              >
                Please enter a valid email
              </Text>
            )}
            <Button
              //disabled={!showSubmit}
              onClick={() => {
                handleForgottenPassword();
              }}
              mt={theme.spacing.ml.px}
            >
              Send email
            </Button>
          </>
        ) : (
          <>
            <Text element="p" variant="lg" textAlign="center">
              If we’ve found an account with your email address, we’ve sent a
              link for you to reset your password with.
            </Text>
            <Button
              disabled={!showSubmit}
              onClick={() => {
                handleForgottenPassword();
              }}
              mt={theme.spacing.ml.px}
            >
              Resend email
            </Button>
          </>
        )}
      </VStack>
    </Card>
  );
};

export const ResetPasswordCard = ({
  onSubmit,
  isSubmitted,
  errorMsg,
}: AuthCardProps) => {
  const initialState = { password: "", confirmPassword: "" };
  const actions = {
    SET_PASSWORD: "SET_PASSWORD",
    SET_CONFIRM_PASSWORD: "SET_CONFIRM_PASSWORD",
  };

  const reducer = (state, action) => {
    switch (action?.type) {
      case actions.SET_PASSWORD:
        return { ...state, password: action?.payload?.value };
      case actions.SET_CONFIRM_PASSWORD:
        return { ...state, confirmPassword: action?.payload?.value };
      default:
        return state;
    }
  };

  const [state, dispatch] = useReducer(reducer, initialState);

  const handleLoginClick = () => {
    router.push("/login");
  };

  if (isSubmitted) {
    return (
      <Card title="Password reset!">
        <VStack
          w="100%"
          gap={0}
          onSubmit={() => onSubmit(state)}
          mb={theme.spacing.custom.authentication.rem}
        >
          <Text
            element="p"
            variant="lg"
            textAlign="center"
            mb={theme.spacing.md.rem}
          >
            All sorted! You can now login with your updated password.
          </Text>
          <Button onClick={handleLoginClick}>Login</Button>
        </VStack>
      </Card>
    );
  }

  return (
    <Card title="New password">
      <VStack
        as="form"
        w="100%"
        gap={0}
        onSubmit={() => onSubmit(state)}
        mb={theme.spacing.custom.authentication.rem}
      >
        <Text
          element="p"
          variant="lg"
          textAlign="center"
          mb={theme.spacing.md.rem}
        >
          Keep it secret, keep it safe.
        </Text>
        <Text
          element="h3"
          variant="xs"
          textAlign="left"
          alignSelf="flex-start"
          mt="18px"
        >
          Password
        </Text>
        <InputField
          defaultValue={state?.password}
          onChange={(e) =>
            dispatch({
              type: "SET_PASSWORD",
              payload: { value: e.target.value },
            })
          }
          type="password"
          variant="outline"
        />
        <Text
          element="h3"
          variant="xs"
          textAlign="left"
          alignSelf="flex-start"
          mt="18px"
        >
          Re-type password
        </Text>
        <InputField
          defaultValue={state?.confirmPassword}
          onChange={(e) =>
            dispatch({
              type: "SET_CONFIRM_PASSWORD",
              payload: { value: e.target.value },
            })
          }
          type="password"
          variant="outline"
          mt="0"
          mb={theme.spacing.md.rem}
        />
        <Button
          onClick={() => {
            onSubmit(state);
          }}
        >
          Reset password
        </Button>
        {errorMsg ? (
          <Text
            element="p"
            variant="sm"
            textAlign="center"
            color={theme.colors.ui.alert_red_01.hex}
            mt={theme.spacing.md.px}
          >
            {errorMsg}
          </Text>
        ) : null}
      </VStack>
    </Card>
  );
};

export const GoodbyeCard = () => {
  return (
    <Card title="Goodbye">
      <Box py={theme.spacing.custom.authentication.rem} w="100%"></Box>
      <VStack gap={3} w="100%" justifyContent="center">
        <div style={{ textAlign: "center" }}>
          <Text element="p" variant="md">
            Your account has been deleted. We&apos;re sorry to see you go.
          </Text>
        </div>
        <Hyperlink href="/login" label="Return to home" />
      </VStack>
    </Card>
  );
};
